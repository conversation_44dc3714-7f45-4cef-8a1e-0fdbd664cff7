<?php
/**
 * Site Configuration
 * Central configuration for A. A. Chips' Blog
 */

return [
    // Site Information
    'site' => [
        'name' => 'A. A. Chips',
        'title' => 'A. A. Chips\' Blog',
        'tagline' => 'Personal stories, advocacy work, and reflections',
        'description' => 'Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.',
        'author' => 'A. A. Chips',
        'url' => '', // Base URL - leave empty for relative paths
        'language' => 'en',
        'charset' => 'UTF-8'
    ],

    // SEO & Meta
    'meta' => [
        'keywords' => 'A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life',
        'robots' => 'index, follow',
        'viewport' => 'width=device-width, initial-scale=1.0'
    ],

    // Social Media & External Services
    'social' => [
        'kofi' => 'https://www.ko-fi.com/aachips',
        'chatway_enabled' => true,
        'clarity_enabled' => true
    ],

    // Navigation
    'navigation' => [
        'Home' => 'index.php',
        'About' => 'about.php',
        'Advocacy' => 'advocacy.php',
        'Personal' => 'personal.php',
        'Gallery' => 'gallery.php'
    ],

    // Categories
    'categories' => [
        'Street Advocacy' => 'advocacy.php',
        'Apple Chip Kitchen' => 'kitchen.php',
        'Alienation' => 'alienation.php',
        'Climate' => 'climate.php',
        'Humor' => '#',
        'Inspiration' => 'inspiration.php',
        'Journal' => 'journal.php',
        'Personal Stories' => 'personal.php',
        'Writings' => 'writings.php'
    ],

    // Build Settings
    'build' => [
        'generate_php' => true, // Generate PHP files instead of static HTML
        'include_post_meta' => true,
        'include_related_posts' => true,
        'excerpt_length' => 150,
        'posts_per_page' => 10
    ],

    // Paths
    'paths' => [
        'css' => 'css/',
        'js' => 'js/',
        'images' => 'img/',
        'includes' => 'includes/',
        'templates' => 'templates/'
    ]
];
