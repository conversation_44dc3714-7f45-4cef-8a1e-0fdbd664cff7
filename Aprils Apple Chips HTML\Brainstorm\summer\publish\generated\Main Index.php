<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = '"Main Index"';
$meta_description = '"Main navigation page for the blog content"';
$meta_keywords = 'navigation, index, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => '"Main Index"',
  'date' => '"2025-05-20"',
  'tags' => 
  array (
    0 => 'navigation',
    1 => 'index',
  ),
  'excerpt' => '"Main navigation page for the blog content"',
);

// Raw content
$post_content = '<h1>Main Index</h1>

<h2>Advocacy & Community</h2>
<ul><li><a href="bugs-and-addiction.php" class="internal-link">Bugs and Addiction</a></li>
<p><li><a href="city-council-letter.php" class="internal-link">city-council-letter</a></li></p>
<p><li><a href="cultivating-community-and-solidarity.php" class="internal-link">Cultivating Community and Solidarity</a></li></p>
<p><li><a href="hobo-code-of-ethics.php" class="internal-link">Hobo Code of Ethics</a></li></p>
<p><li><a href="responding-to-panhandlers.php" class="internal-link">Responding to Panhandlers</a></li></p>
<p><li><a href="wellness-checks.php" class="internal-link">wellness-checks</a></li></p>

<h2>Health & Wellbeing</h2>
<p><li><a href="how-to-handle-a-dissociative-episode.php" class="internal-link">How to Handle a Dissociative Episode</a></li></p>
<p><li><a href="should-social-workers-diagnose-mental-illness.php" class="internal-link">Should Social Workers Diagnose Mental Illness</a></li></p>
<p><li><a href="traumatic-brain-injury-in-homeless-people-is-underrecognized-neurologytoday.php" class="internal-link">Traumatic Brain Injury in Homeless People is Underrecognized - NeurologyToday</a></li></p>
<p><li><a href="us-surgeon-general-releases-new-framework-for-mental-health-well-being-in-the-workplace-102022-press-release-hhs.php" class="internal-link">US Surgeon General Releases New Framework for Mental Health _ Well-being in the Workplace 102022 Press Release HHS</a></li></p>

<h2>Environmental Topics</h2>
<p><li><a href="why-possums.php" class="internal-link">Why Possums</a></li></p>
<p><li><a href="the-swannanoa-mulch-fire.php" class="internal-link">The Swannanoa Mulch Fire</a></li></p>
<p><li><a href="rethinking-digital-ecosystems-a-call-for-ecological-literacy-in-tech.php" class="internal-link">Rethinking Digital Ecosystems - A Call for Ecological Literacy in Tech</a></li></p>
<p><li><a href="if-you-have-never-been-in-a-hurricane-nappy-thoughts.php" class="internal-link">If you have never been in a hurricane - Nappy Thoughts</a></li></p>

<h2>Personal Reflections</h2>
<p><li><a href="if-you-choose-to-be-homeless-it-means-your-choices-in-the-situation-were-terrible-1.php" class="internal-link">If you choose to be homeless, it means your choices in the situation were terrible 1</a></li></p>
<p><li><a href="stay-warm.php" class="internal-link">stay-warm</a></li></ul></p>

<p>!<a href="possumfriends-png.php" class="internal-link">possumfriends.png</a></p>

<p>---</p>

<p><em>This index page has been reorganized to group related content. More sections will be added as content is further organized.</em></p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>