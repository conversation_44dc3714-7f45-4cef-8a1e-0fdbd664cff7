<?php
/**
 * Enhanced Blog Builder
 * Converts Markdown files with YAML frontmatter to dynamic PHP pages
 */

class BlogBuilder {
    private $contentDir = 'content';
    private $outputDir = 'generated';
    private $dataDir = 'data';
    private $templateFile = 'page template.htm';
    private $configFile = 'config.php';
    private $siteConfig = [];

    public function __construct() {
        // Load site configuration
        if (file_exists($this->configFile)) {
            $this->siteConfig = include $this->configFile;
        } else {
            throw new Exception("Configuration file not found: {$this->configFile}");
        }

        // Create output directories if they don't exist
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }

    public function build() {
        echo "Starting blog build...\n";

        // Process all markdown files
        $this->processMarkdownFiles();

        // Generate JSON data for shorthand content
        $this->generateHumorData();

        // Generate main index page
        $this->generateIndexPage();

        echo "Build complete!\n";
    }

    private function processMarkdownFiles() {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($this->contentDir)
        );

        foreach ($iterator as $file) {
            if ($file->getExtension() === 'md') {
                $this->processMarkdownFile($file);
            }
        }
    }

    private function processMarkdownFile($file) {
        $content = file_get_contents($file->getPathname());
        $parsed = $this->parseMarkdown($content);

        // Skip if no frontmatter
        if (!$parsed['frontmatter']) {
            return;
        }

        $frontmatter = $parsed['frontmatter'];
        $markdownContent = $parsed['content'];

        // Get relative path for image handling
        $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
        $relativePath = dirname($relativePath);
        if ($relativePath === '.') {
            $relativePath = '';
        }

        // Convert markdown to HTML
        $htmlContent = $this->markdownToHtml($markdownContent, $relativePath);

        // Determine if this should be shorthand content (humor section)
        $isShorthand = $this->isShorthandContent($file, $frontmatter);

        if ($isShorthand) {
            // Store for JSON generation instead of creating HTML file
            return;
        }

        // Generate HTML file
        $this->generateHtmlFile($file, $frontmatter, $htmlContent);
    }

    private function parseMarkdown($content) {
        // Simple YAML frontmatter parser
        if (!preg_match('/^---\s*\n(.*?)\n---\s*\n(.*)$/s', $content, $matches)) {
            return ['frontmatter' => null, 'content' => $content];
        }

        $frontmatter = [];
        $yamlLines = explode("\n", $matches[1]);
        $currentKey = null;

        foreach ($yamlLines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Handle array items
            if (strpos($line, '-') === 0) {
                if ($currentKey && !isset($frontmatter[$currentKey])) {
                    $frontmatter[$currentKey] = [];
                }
                if ($currentKey) {
                    $frontmatter[$currentKey][] = trim(substr($line, 1));
                }
            } elseif (strpos($line, ':') !== false) {
                [$key, $value] = explode(':', $line, 2);
                $key = trim($key);
                $value = trim($value);
                $currentKey = $key;

                if (!empty($value)) {
                    $frontmatter[$key] = $value;
                }
            }
        }

        return ['frontmatter' => $frontmatter, 'content' => $matches[2]];
    }

    private function markdownToHtml($markdown, $relativePath = '') {
        // Simple markdown to HTML conversion
        $html = $markdown;

        // Headers (but don't convert if it's already HTML)
        $html = preg_replace('/^### (.*$)/m', '<h3>$1</h3>', $html);
        $html = preg_replace('/^## (.*$)/m', '<h2>$1</h2>', $html);
        // Don't convert # headers if they're already in HTML format
        $html = preg_replace('/^# (?!.*<h1>)(.*$)/m', '<h1>$1</h1>', $html);

        // Bold and italic
        $html = preg_replace('/\*\*(.*?)\*\*/', '<strong>$1</strong>', $html);
        $html = preg_replace('/\*(.*?)\*/', '<em>$1</em>', $html);

        // Links
        $html = preg_replace('/\[([^\]]+)\]\(([^)]+)\)/', '<a href="$2">$1</a>', $html);

        // Images - handle relative paths correctly
        $imgBasePath = $this->calculateImagePath($relativePath);
        $html = preg_replace_callback('/!\[\[([^\]]+)\]\]/', function($matches) use ($imgBasePath) {
            $imageName = preg_replace('/\|.*$/', '', $matches[1]); // Remove size specifications
            return '<img src="' . $imgBasePath . $imageName . '" alt="' . htmlspecialchars($imageName) . '">';
        }, $html);
        $html = preg_replace('/!\[([^\]]*)\]\(([^)]+)\)/', '<img src="$2" alt="$1">', $html);

        // Lists
        $html = preg_replace('/^\+ (.*)$/m', '<li>$1</li>', $html);
        $html = preg_replace('/^- (.*)$/m', '<li>$1</li>', $html);

        // Wrap consecutive list items in ul tags
        $html = preg_replace('/(<li>.*<\/li>)/s', '<ul>$1</ul>', $html);

        // Paragraphs
        $lines = explode("\n", $html);
        $result = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) {
                $result[] = '';
                continue;
            }

            // Don't wrap HTML tags in paragraphs
            if (strpos($line, '<h') === 0 || strpos($line, '<ul>') === 0 ||
                strpos($line, '<img') === 0 || strpos($line, '<iframe') === 0 ||
                strpos($line, '<div') === 0 || strpos($line, '<hr') === 0 ||
                strpos($line, '</div>') === 0) {
                $result[] = $line;
            } else {
                $result[] = "<p>{$line}</p>";
            }
        }

        return implode("\n", $result);
    }

    private function calculateImagePath($relativePath) {
        // Calculate the correct relative path to img/ directory
        if (empty($relativePath)) {
            return 'img/';
        }

        // Count directory levels to determine how many ../ we need
        $levels = substr_count($relativePath, DIRECTORY_SEPARATOR);
        $basePath = str_repeat('../', $levels);

        return $basePath . 'img/';
    }

    private function isShorthandContent($file, $frontmatter) {
        // Check if file is in humor directory or has humor tag
        $path = $file->getPathname();
        if (strpos($path, 'humor') !== false) {
            return true;
        }

        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            // Handle both array and string tags
            if (is_array($tags) && in_array('humor', $tags)) {
                return true;
            } elseif (is_string($tags) && $tags === 'humor') {
                return true;
            }
        }

        return false;
    }

    private function generateHtmlFile($file, $frontmatter, $content) {
        // Generate output filename
        $relativePath = str_replace($this->contentDir . DIRECTORY_SEPARATOR, '', $file->getPathname());
        $extension = $this->siteConfig['build']['generate_php'] ? '.php' : '.html';
        $outputPath = $this->outputDir . DIRECTORY_SEPARATOR . str_replace('.md', $extension, $relativePath);

        // Create directory if needed
        $outputDir = dirname($outputPath);
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }

        // Generate PHP or HTML file
        if ($this->siteConfig['build']['generate_php']) {
            // Generate PHP file with dynamic content
            $phpContent = $this->generatePhpFile($frontmatter, $content);
            file_put_contents($outputPath, $phpContent);
        } else {
            // Generate static HTML file
            $this->generateStaticHtmlFile($file, $frontmatter, $content, $outputPath);
        }

        echo "Generated: $outputPath\n";
    }

    private function generateStaticHtmlFile($file, $frontmatter, $content, $outputPath) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template
        $page_title = $frontmatter['title'] ?? 'Untitled';
        $css_path = '../css/';
        $js_path = '../js/';
        $base_url = '../';

        // For static HTML generation, create the full content with post header
        $postHeader = $this->generatePostHeader($frontmatter);
        $postFooter = "\n</div><!-- .post-content -->\n";
        $fullContent = $postHeader . $content . $postFooter;

        // Prepare meta data
        $meta_description = $frontmatter['excerpt'] ?? $frontmatter['description'] ?? '';
        $meta_keywords = '';
        if (isset($frontmatter['tags'])) {
            $tags = is_array($frontmatter['tags']) ? $frontmatter['tags'] : [$frontmatter['tags']];
            $meta_keywords = implode(', ', $tags) . ', ' . ($this->siteConfig['meta']['keywords'] ?? '');
        }

        // Start output buffering to capture PHP includes
        ob_start();

        // Set variables for template
        extract([
            'config' => $this->siteConfig,
            'page_title' => $page_title,
            'content' => $fullContent,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'meta_description' => $meta_description,
            'meta_keywords' => $meta_keywords,
            'related_posts' => []
        ]);

        // Evaluate the template
        eval('?>' . $template);
        $html = ob_get_clean();

        // Write static HTML file
        file_put_contents($outputPath, $html);
    }

    private function generatePhpFile($frontmatter, $content) {
        // Prepare variables for PHP file
        $page_title = $frontmatter['title'] ?? 'Untitled';
        $meta_description = $frontmatter['excerpt'] ?? $frontmatter['description'] ?? '';
        $meta_keywords = '';
        if (isset($frontmatter['tags'])) {
            $tags = is_array($frontmatter['tags']) ? $frontmatter['tags'] : [$frontmatter['tags']];
            $meta_keywords = implode(', ', $tags) . ', ' . ($this->siteConfig['meta']['keywords'] ?? '');
        }

        // Create PHP file content with dynamic post generation
        $phpContent = "<?php\n";
        $phpContent .= "// Auto-generated blog post\n";
        $phpContent .= "// Source: " . basename($frontmatter['source_file'] ?? 'unknown') . "\n\n";

        $phpContent .= "// Load configuration\n";
        $phpContent .= "\$config = include '../config.php';\n\n";

        $phpContent .= "// Page variables\n";
        $phpContent .= "\$page_title = " . var_export($page_title, true) . ";\n";
        $phpContent .= "\$meta_description = " . var_export($meta_description, true) . ";\n";
        $phpContent .= "\$meta_keywords = " . var_export($meta_keywords, true) . ";\n";
        $phpContent .= "\$css_path = '../css/';\n";
        $phpContent .= "\$js_path = '../js/';\n";
        $phpContent .= "\$base_url = '../';\n";
        $phpContent .= "\$related_posts = [];\n\n";

        $phpContent .= "// Post metadata\n";
        $phpContent .= "\$post_data = " . var_export($frontmatter, true) . ";\n\n";

        $phpContent .= "// Raw content\n";
        $phpContent .= "\$post_content = " . var_export($content, true) . ";\n\n";

        $phpContent .= "// Generate dynamic content\n";
        $phpContent .= "ob_start();\n";
        $phpContent .= "?>\n";
        $phpContent .= "<article class=\"post-header\">\n";
        $phpContent .= "    <?php if (isset(\$post_data['title'])): ?>\n";
        $phpContent .= "        <h1 class=\"post-title\"><?php echo htmlspecialchars(\$post_data['title']); ?></h1>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php \$metadata = []; ?>\n";
        $phpContent .= "    <?php if (isset(\$post_data['author'])): ?>\n";
        $phpContent .= "        <?php \$metadata[] = '<span class=\"post-author\"><i class=\"icon-user\"></i>By ' . htmlspecialchars(\$post_data['author']) . '</span>'; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    <?php if (isset(\$post_data['date'])): ?>\n";
        $phpContent .= "        <?php \$formatted_date = (strtotime(\$post_data['date']) !== false) ? date('F j, Y', strtotime(\$post_data['date'])) : htmlspecialchars(\$post_data['date']); ?>\n";
        $phpContent .= "        <?php \$metadata[] = '<span class=\"post-date\"><i class=\"icon-calendar\"></i>' . \$formatted_date . '</span>'; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (!empty(\$metadata)): ?>\n";
        $phpContent .= "        <div class=\"post-meta\"><?php echo implode('<span class=\"meta-separator\"> • </span>', \$metadata); ?></div>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (isset(\$post_data['excerpt'])): ?>\n";
        $phpContent .= "        <div class=\"post-excerpt\">\n";
        $phpContent .= "            <p><em><?php echo htmlspecialchars(\$post_data['excerpt']); ?></em></p>\n";
        $phpContent .= "        </div>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "    \n";
        $phpContent .= "    <?php if (isset(\$post_data['tags'])): ?>\n";
        $phpContent .= "        <?php \$tags = is_array(\$post_data['tags']) ? \$post_data['tags'] : [\$post_data['tags']]; ?>\n";
        $phpContent .= "        <?php if (!empty(\$tags)): ?>\n";
        $phpContent .= "            <div class=\"post-tags\">\n";
        $phpContent .= "                <span class=\"tags-label\">Tags:</span>\n";
        $phpContent .= "                <?php foreach (\$tags as \$tag): ?>\n";
        $phpContent .= "                    <?php \$tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim(\$tag)))); ?>\n";
        $phpContent .= "                    <a href=\"tag-<?php echo \$tag_slug; ?>.html\" class=\"tag\"><?php echo htmlspecialchars(\$tag); ?></a>\n";
        $phpContent .= "                <?php endforeach; ?>\n";
        $phpContent .= "            </div>\n";
        $phpContent .= "        <?php endif; ?>\n";
        $phpContent .= "    <?php endif; ?>\n";
        $phpContent .= "</article>\n\n";
        $phpContent .= "<div class=\"post-content\">\n";
        $phpContent .= "    <?php echo \$post_content; ?>\n";
        $phpContent .= "</div><!-- .post-content -->\n";
        $phpContent .= "<?php\n";
        $phpContent .= "\$content = ob_get_clean();\n\n";

        $phpContent .= "// Include template\n";
        $phpContent .= "include '../page template.htm';\n";
        $phpContent .= "?>";

        return $phpContent;
    }

    private function generatePostHeader($frontmatter) {
        if (empty($frontmatter)) {
            return '';
        }

        $header = "<article class=\"post-header\">\n";

        // Add title
        if (isset($frontmatter['title'])) {
            $header .= "    <h1 class=\"post-title\">" . htmlspecialchars($frontmatter['title']) . "</h1>\n";
        }

        // Add post metadata
        $metadata = [];
        if (isset($frontmatter['author'])) {
            $metadata[] = "<span class=\"post-author\"><i class=\"icon-user\"></i>By " . htmlspecialchars($frontmatter['author']) . "</span>";
        }
        if (isset($frontmatter['date'])) {
            $formattedDate = $this->formatDate($frontmatter['date']);
            $metadata[] = "<span class=\"post-date\"><i class=\"icon-calendar\"></i>{$formattedDate}</span>";
        }
        if (isset($frontmatter['reading_time'])) {
            $metadata[] = "<span class=\"post-reading-time\"><i class=\"icon-clock\"></i>{$frontmatter['reading_time']} min read</span>";
        }

        if (!empty($metadata)) {
            $header .= "    <div class=\"post-meta\">" . implode('<span class=\"meta-separator\"> • </span>', $metadata) . "</div>\n";
        }

        // Add excerpt if available
        if (isset($frontmatter['excerpt'])) {
            $header .= "    <div class=\"post-excerpt\">\n";
            $header .= "        <p><em>" . htmlspecialchars($frontmatter['excerpt']) . "</em></p>\n";
            $header .= "    </div>\n";
        }

        // Add tags if available
        if (isset($frontmatter['tags'])) {
            $tags = $frontmatter['tags'];
            if (is_array($tags) && !empty($tags)) {
                $header .= "    <div class=\"post-tags\">\n";
                $header .= "        <span class=\"tags-label\">Tags:</span>\n";
                foreach ($tags as $tag) {
                    $tagSlug = $this->slugify($tag);
                    $header .= "        <a href=\"tag-{$tagSlug}.html\" class=\"tag\">" . htmlspecialchars($tag) . "</a>\n";
                }
                $header .= "    </div>\n";
            } elseif (is_string($tags)) {
                $tagSlug = $this->slugify($tags);
                $header .= "    <div class=\"post-tags\">\n";
                $header .= "        <span class=\"tags-label\">Tags:</span>\n";
                $header .= "        <a href=\"tag-{$tagSlug}.html\" class=\"tag\">" . htmlspecialchars($tags) . "</a>\n";
                $header .= "    </div>\n";
            }
        }

        $header .= "</article>\n\n";
        $header .= "<div class=\"post-content\">\n\n";

        return $header;
    }

    private function formatDate($date) {
        // Try to parse and format the date
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            return date('F j, Y', $timestamp);
        }
        return htmlspecialchars($date); // Return as-is if parsing fails
    }

    private function slugify($text) {
        // Convert to lowercase and replace spaces/special chars with hyphens
        $slug = strtolower(trim($text));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }

    private function generateHumorData() {
        $humorData = [];
        $humorDir = $this->contentDir . '/humor';

        if (!is_dir($humorDir)) {
            file_put_contents($this->dataDir . '/humor.json', json_encode([]));
            return;
        }

        $files = glob($humorDir . '/*.md');

        foreach ($files as $file) {
            $content = file_get_contents($file);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content'], 'humor');

                $humorData[] = [
                    'title' => $frontmatter['title'] ?? basename($file, '.md'),
                    'author' => $frontmatter['author'] ?? null,
                    'date' => $frontmatter['date'] ?? null,
                    'excerpt' => $this->generateExcerpt($parsed['content']),
                    'content' => $htmlContent,
                    'tags' => $frontmatter['tags'] ?? []
                ];
            }
        }

        file_put_contents($this->dataDir . '/humor.json', json_encode($humorData, JSON_PRETTY_PRINT));
        echo "Generated humor data: " . count($humorData) . " items\n";
    }

    private function generateIndexPage() {
        // Check if index.md exists in content directory
        $indexFile = $this->contentDir . '/index.md';

        if (file_exists($indexFile)) {
            // Process the existing index.md file
            $content = file_get_contents($indexFile);
            $parsed = $this->parseMarkdown($content);

            if ($parsed['frontmatter']) {
                $frontmatter = $parsed['frontmatter'];
                $htmlContent = $this->markdownToHtml($parsed['content'], '');

                // Generate index.html in root directory
                $this->generateRootIndexFile($frontmatter, $htmlContent);
            }
        } else {
            // Generate a default index page
            $this->generateDefaultIndexPage();
        }
    }

    private function generateRootIndexFile($frontmatter, $content) {
        // Load template
        $template = file_get_contents($this->templateFile);

        // Prepare variables for template
        $page_title = $frontmatter['title'] ?? $this->siteConfig['site']['title'] ?? 'Welcome to A. A. Chips\' Blog';
        $css_path = 'css/';
        $js_path = 'js/';
        $base_url = '';

        // Prepare meta data
        $meta_description = $frontmatter['description'] ?? $this->siteConfig['site']['description'] ?? '';
        $meta_keywords = $this->siteConfig['meta']['keywords'] ?? '';

        // Start output buffering to capture PHP includes
        ob_start();

        // Set variables for template
        extract([
            'config' => $this->siteConfig,
            'page_title' => $page_title,
            'content' => $content,
            'css_path' => $css_path,
            'js_path' => $js_path,
            'base_url' => $base_url,
            'meta_description' => $meta_description,
            'meta_keywords' => $meta_keywords,
            'related_posts' => []
        ]);

        // Evaluate the template
        eval('?>' . $template);
        $html = ob_get_clean();

        // Write index.html file in root
        file_put_contents('index.html', $html);
        echo "Generated: index.html\n";
    }

    private function generateDefaultIndexPage() {
        $defaultContent = '<h1>Welcome to A. A. Chips\' Digital Garden</h1>
        <p>This is a simple blog system built from Markdown files.</p>
        <p>Check out the generated content in the <a href="generated/">generated folder</a>.</p>';

        $this->generateRootIndexFile(['title' => 'A. A. Chips\' Blog'], $defaultContent);
    }

    private function generateExcerpt($content, $length = 150) {
        $text = strip_tags($content);
        $text = preg_replace('/\s+/', ' ', $text);

        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length) . '...';
    }
}

// Run the builder
if (php_sapi_name() === 'cli') {
    $builder = new BlogBuilder();
    $builder->build();
} else {
    echo "This script should be run from the command line.";
}
?>
