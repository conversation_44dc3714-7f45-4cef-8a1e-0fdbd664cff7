<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Austin experimented with giving people $1,000 a month. They said they spent the no-strings-attached cash mostly on housing.';
$meta_description = 'A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.';
$meta_keywords = 'homeless, basicincome, advocacy, library, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Austin experimented with giving people $1,000 a month. They said they spent the no-strings-attached cash mostly on housing.',
  'author' => '<PERSON> - Business Insider',
  'excerpt' => 'A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'basicincome',
    2 => 'advocacy',
    3 => 'library',
  ),
);

// Raw content
$post_content = '<p>A guaranteed basic income plan in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.</p>
<h3>Story by <a href="https://www.businessinsider.com/author/kenneth-niemeyer" class="external-link">Kenneth Niemeyer</a></h3>

<p>* A guaranteed basic income program in Austin gave people $1,000 a month for a year.</p>
<p>* Most of the participants spent the no-string-attached cash on housing, a study of the program found.</p>
<p>* Participants who said they could afford a balanced meal also increased by 17%.</p>

<p>A <a href="https://www.businessinsider.com/denver-basic-income-project-ubi-extended-homelessness-poverty-2024-1" class="external-link">guaranteed basic income plan</a> in one of Texas\'s largest cities reduced rates of housing insecurity. But some Texas lawmakers are not happy.</p>

<p>Austin was the first city in Texas to launch a <a href="https://www.businessinsider.com/universal-basic-income-texas-houston-harris-county-covid-relief-2023-12" class="external-link">tax-payer-funded basic income program</a> when the Austin Guaranteed Income Pilot kicked off in May 2022. The program served 135 low-income families, each receiving up to $1,000 monthly. Funding for 85 families came from the City of Austin while philanthropic donations funded the other 50.</p>

<p>Read more at <https://www.businessinsider.com/austin-guarunteed-basic-income-gbi-ubi-housing-security-homeless-2024-1></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>