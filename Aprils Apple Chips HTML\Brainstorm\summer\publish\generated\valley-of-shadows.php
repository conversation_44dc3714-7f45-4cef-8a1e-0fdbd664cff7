<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = '"\'Free from the stories I\'ve been told, I walk through the valley of my own shadows\' - Morning Journal 5-22-2025"';
$meta_description = 'Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. "It’s hard for a parent," <PERSON><PERSON> says. But she doesn’t flinch from the truth: "Harder for the one sleeping in the street."';
$meta_keywords = 'homeless, journal, advocacy, parentalalienation, writings, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => '"\'Free from the stories I\'ve been told, I walk through the valley of my own shadows\' - Morning Journal 5-22-2025"',
  'date' => '2025-05-22',
  'excerpt' => 'Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. "It’s hard for a parent," <PERSON><PERSON> says. But she doesn’t flinch from the truth: "Harder for the one sleeping in the street."',
  'tags' => 
  array (
    0 => 'homeless',
    1 => 'journal',
    2 => 'advocacy',
    3 => 'parentalalienation',
    4 => 'writings',
  ),
  'slug' => '/valley-of-shadows',
);

// Raw content
$post_content = '<p><a href="http://localhost/">A. A. Chips</a></p>

<ul><li><a href="http://localhost/">Home</a></li>
<p><li><a href="http://localhost/about">About</a></li></p>
<p><li><a href="http://localhost/advocacy">Advocacy</a></li></p>
<p><li><a href="http://localhost/personal">Personal</a></li></p>
<p><li><a href="http://localhost/gallery">Gallery</a></li></p>

<p>Categories: Writings, Journal, Street Advocacy, Alienation</p>


<h3>"Free from the stories I\'ve been told, I walk through the valley of my own shadows"</h3>

<p>Mera knocks softly at my door just after dawn. We’ve been waking like this all week, drawn into conversation by the weight of dream fragments still clinging to us. Six years ago, I found her wallet in the church kitchen where I packed COVID food boxes. Back then, she was a ghost—hollow-eyed, living in a storage unit, her body a battleground for addiction and violence. Now, she’s solid. Clean. Sitting cross-legged on my floor in acrylic painted sweatpants, her hair a messy crown under the hallway Christmas lights.</p>
<p>#### We talk about mothers.</p>

<p>Her mother carried homelessness like a stone in her pocket, fingering its edges until they drew blood. _"It’s hard for a parent,"_ Mera says. But she doesn’t flinch from the truth: "It\'s ten times harder for the person sleeping outside, on concrete, in a storage unit, or in a car." I think of Gaza, of mass starvation—how bearing witness is not the same as being buried in the rubble. Understanding came for Mera’s mother only when she saw the cracks in her daughter’s hands, the way the streets had carved into her.</p>

<p>My own mother? I keep the door locked. Not out of cruelty, but survival. I know her rhythms too well: the police calls, the tears, the way her stories warp reality until someone else pays the price. In her presence, I imagine sirens, a gun misfired, a friend’s body crumpling under the weight of her narrative.</p>

<p>That’s why I’m building this site. Not to plead my case, not to chase rumors—but to lay the truth bare, like a bone on a table. Let them think what they want. But for the ones still listening, I’ll say it plain: _This is what it looks like from the ground._</p>

<img src="img/standingbear.jpg" alt="standingbear.jpg">

<h3>About A. A. Chips</h3>

<p>Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.</p>

<h2>Donate</h2>

<p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="https://www.ko-fi.com/aachips">Ko-Fi page</a>.</p>

<p>[[Crowdfunding Campaign - A. A. Chips 1|What am I raising funds for?]]</p>

<h3>Categories</h3>


<p><li><a href="http://localhost/advocacy">Advocacy</a></li></p>
<p><li><a href="/kitchen">Apple Chip Kitchen</a></li></p>
<p><li><a href="/alienation">Alienation</a></li></p>
<p><li><a href="/climate">Climate</a></li></p>
<p><li><a href="/humor">Humor</a></li></p>
<p><li><a href="/inspire">Inspiration</a></li></p>
<p><li><a href="/journal">Journal</a></li></p>
<p><li><a href="/maze">Maze of Wonders</a></li></p>
<p><li><a href="http://localhost/personal">Personal Stories</a></li></p>
<p><li><a href="/writings">Writings</a></li></ul></p>

<h3>Connect</h3>

<p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email.</p>


<p>© 2025 A. A. Chips. All rights reserved.</p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>