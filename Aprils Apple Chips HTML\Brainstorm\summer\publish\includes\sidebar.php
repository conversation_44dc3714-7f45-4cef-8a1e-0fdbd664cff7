<section id="related">
    <!-- Breadcrumb Navigation -->
    <?php if (isset($breadcrumb) && !empty($breadcrumb)): ?>
    <div class="breadcrumb">
        <?php foreach ($breadcrumb as $index => $crumb): ?>
            <?php if ($index > 0): ?><span class="breadcrumb-separator">›</span><?php endif; ?>
            <?php if (isset($crumb['url'])): ?>
                <a href="<?php echo $crumb['url']; ?>"><?php echo htmlspecialchars($crumb['title']); ?></a>
            <?php else: ?>
                <span><?php echo htmlspecialchars($crumb['title']); ?></span>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- Enhanced Related Posts -->
    <div class="related-posts-enhanced">
        <h3>Related Posts</h3>
        <div class="related-posts-grid">
            <?php if (isset($related_posts) && !empty($related_posts)): ?>
                <?php foreach ($related_posts as $post): ?>
                    <div class="related-post-item">
                        <h4><a href="<?php echo $post['url']; ?>"><?php echo htmlspecialchars($post['title']); ?></a></h4>
                        <?php if (isset($post['excerpt'])): ?>
                            <div class="related-post-excerpt"><?php echo htmlspecialchars($post['excerpt']); ?></div>
                        <?php endif; ?>
                        <?php if (isset($post['date']) || isset($post['author'])): ?>
                            <div class="related-post-meta">
                                <?php if (isset($post['author'])): ?>By <?php echo htmlspecialchars($post['author']); ?><?php endif; ?>
                                <?php if (isset($post['date'])): ?> • <?php echo htmlspecialchars($post['date']); ?><?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="related-post-item">
                    <h4><a href="<?php echo ($base_url ?? '') . 'advocacy.php'; ?>">Advocacy Posts</a></h4>
                    <div class="related-post-excerpt">Street advocacy and social justice content.</div>
                </div>
                <div class="related-post-item">
                    <h4><a href="<?php echo ($base_url ?? '') . 'personal.php'; ?>">Personal Stories</a></h4>
                    <div class="related-post-excerpt">Personal reflections and experiences.</div>
                </div>
                <div class="related-post-item">
                    <h4><a href="<?php echo ($base_url ?? '') . 'inspiration-vault.php'; ?>">Inspiration Vault</a></h4>
                    <div class="related-post-excerpt">Uplifting content and motivational pieces.</div>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <div>
        <section id="about">
            <h3>About <?php echo htmlspecialchars($config['site']['name'] ?? 'A. A. Chips'); ?></h3>
            <p><?php echo htmlspecialchars($config['site']['description'] ?? 'Personal stories, advocacy work, and reflections on homelessness, family alienation, and rebuilding life. Join me on this journey of expression and connection.'); ?></p>
        </section>

        <section id="donate">
            <h3>Donate</h3>
            <p>If you like what I am doing. Or if you hate what I am doing. You can donate one-time or recurring pledges through my <a href="<?php echo $config['social']['kofi'] ?? 'https://www.ko-fi.com/aachips'; ?>">Ko-Fi page</a>.</p>
            <a href="crowdfund.html" target="_blank">What am I raising funds for?</a>
        </section>

        <section id="categories">
            <h3>Content Categories</h3>
            <ul>
                <?php
                $categories = $config['categories'] ?? [
                    'Street Advocacy' => 'advocacy.html',
                    'Apple Chip Kitchen' => 'kitchen.html',
                    'Alienation' => 'alienation.html',
                    'Climate' => 'climate.html',
                    'Humor' => '#',
                    'Inspiration' => 'inspiration.html',
                    'Journal' => 'journal.html',
                    'Personal Stories' => 'personal.html',
                    'Writings' => 'writings.html'
                ];
                foreach ($categories as $category => $url):
                ?>
                    <li>
                        <?php if ($url === '#'): ?>
                            <a href="#" onclick="showHumorModal()" class="internal-link"><?php echo htmlspecialchars($category); ?></a>
                        <?php else: ?>
                            <a href="<?php echo ($base_url ?? '') . $url; ?>" class="internal-link"><?php echo htmlspecialchars($category); ?></a>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="content-index">
            <h3>Quick Navigation</h3>
            <ul>
                <li><a href="<?php echo ($base_url ?? '') . 'contents.php'; ?>" class="internal-link">Site Contents</a></li>
                <li><a href="<?php echo ($base_url ?? '') . 'index2.php'; ?>" class="internal-link">Alternative Index</a></li>
                <li><a href="<?php echo ($base_url ?? '') . 'Main Index.php'; ?>" class="internal-link">Main Index</a></li>
                <li><a href="<?php echo ($base_url ?? '') . 'humor-vault.php'; ?>" class="internal-link">Humor Vault</a></li>
                <li><a href="<?php echo ($base_url ?? '') . 'inspiration-vault.php'; ?>" class="internal-link">Inspiration Vault</a></li>
            </ul>
        </section>

        <section id="recent-posts">
            <h3>Recent Posts</h3>
            <ul>
                <?php
                // Get recent posts (this would be enhanced with actual recent post data)
                $recent_posts = [
                    ['title' => '200 Blunt Words', 'url' => '200-blunt-words.php'],
                    ['title' => 'ADHD in Women', 'url' => 'adhd-in-women.php'],
                    ['title' => 'Alternatives to I Love You', 'url' => 'alternatives-to-iloveyou.php'],
                    ['title' => 'Hadestown Review', 'url' => 'hadestown-review.php'],
                    ['title' => 'Bite-Sized Learning', 'url' => 'bite-sized-learning.php']
                ];
                foreach ($recent_posts as $post):
                ?>
                    <li><a href="<?php echo ($base_url ?? '') . $post['url']; ?>" class="internal-link"><?php echo htmlspecialchars($post['title']); ?></a></li>
                <?php endforeach; ?>
            </ul>
        </section>

        <section id="connect">
            <h3>Connect</h3>
            <p>Share your thoughts, questions, or just say hello. Use the chat widget below. Leave an email address to reply to.</p>
        </section>
    </div>
</section>
