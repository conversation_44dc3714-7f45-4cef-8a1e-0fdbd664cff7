<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'FBI Releases List of 10 Weirdest People Who Are Actually Harmless Once You Get to Know Them';
$meta_description = '';
$meta_keywords = '"#humor", A. A<PERSON> Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'FBI Releases List of 10 Weirdest People Who Are Actually Harmless Once You Get to Know Them',
  'author' => 'The Onion',
  'source' => 'https://theonion.com/fbi-releases-list-of-10-weirdest-people-who-are-actuall-1850132563/',
  '"Date' => '": February 28, 2023',
  'tags' => 
  array (
    0 => '"#humor"',
  ),
);

// Raw content
$post_content = '<h1>FBI Releases List Of 10 Weirdest People Who Are Actually Harmless Once You Get To Know Them</h1>

<p>!<a href="weirdestpeople-webp.php" class="internal-link">weirdestpeople.webp</a></p>

<p>WASHINGTON—Emphasizing that it was important to always stay vigilant but not freak out about them or anything, the FBI released a list Tuesday of the 10 weirdest people who are actually harmless once you get to know them.</p>

<p>“After countless hours of surveillance and research, we have determined that the following people are the most off-putting freaks in America but if you see them, you ultimately have nothing to worry about,” said FBI director <PERSON> Wray, adding that while each individual had extensive histories of saying odd phrases, pacing, or dressing in crazy outfits, they ultimately wouldn’t hurt anyone.</p>

<p>“We’d like to reiterate that the top wackadoos on this list definitely freaked us out at first, with their odd hairdos, the big books they were always reading, and the fact that many of them would randomly skip down the street and sing a song. But in the end, we determined they were absolutely not a danger to anyone, and were actually pretty friendly. You maybe just don’t want to engage with them for too long.”</p>

<p>At press time, Wray announced that the FBI had assassinated the highest ranking weird guy after he put on a funky hat and started walking towards a nice suburban neighborhood.</p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>