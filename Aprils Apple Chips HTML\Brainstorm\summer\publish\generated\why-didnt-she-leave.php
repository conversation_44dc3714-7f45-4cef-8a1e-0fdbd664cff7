<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Why Didn\'t She Just Leave?';
$meta_description = '';
$meta_keywords = 'memes, dv, library, resources, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Why Didn\'t She Just Leave?',
  'author' => 'Anonymous',
  'tags' => 
  array (
    0 => 'memes',
    1 => 'dv',
    2 => 'library',
    3 => 'resources',
  ),
);

// Raw content
$post_content = '<h1>Why Didn\'t She Just Leave?</h1>

<p>I found this reposted on a social media site and liked it.</p>

<p>Related: [[If you choose to be homeless, it means your choices in the situation were terrible 1|If you choose to be homeless, it means your choices in the situation were terrible.]]</p>

<p>Anonymous:</p>

<p>“Why we should never say "But why don\'t you just leave?" to women in toxic, highly problematic marriages.</p>

<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She was taught to fight for her marriage.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She believes her problems are normal. Nothing that can\'t be dealt with through prayer and faith.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She can\'t support herself or her children.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She sees herself as a contributor to the problems; she\'s hoping he will improve as she improves.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She\'s doesn\'t have anywhere to go.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She\'s too overwhelmed to think clearly about her next step.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She believes God hates divorce.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> Her spouse has promised to complicate her life if she leaves.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She doesn\'t want to fail.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She\'s heard, "marriage is hard, divorce is hard, choose your hard." She\'s chosen her hard.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> Her church doesn\'t allow divorce other than for infidelity (and even then, they would first have to try and reconcile.)</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> Her spiritual sounding board, her church, is silent on abuse and consequences of unrepentant harmful sin in marriage. She\'s in the dark.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> Other people have gone through worse, and they stayed put.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She wants her children to have both parents.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She\'s heard testimonies where people stayed and prayed, and their spouses changed after a long time.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She\'s dependent on her husband for documentation as an immigrant.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> He\'s never hit her.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> Sometimes she thinks the marriage problems are not that bad.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> He\'s a man of God, and she doesn\'t want to ruin his ministry.</p>
<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/te0/1/16/1f31f.png">🌟</a> She loves him.</p>

<p>!<a href="https://static.xx.fbcdn.net/images/emoji.php/v9/t8f/1/16/1f449_1f3fe.png">👉🏾</a> You see, there are bazillion reasons why a wife won\'t "just leave." It\'s more complicated than we think.”</p>

<img src="img/swancpr.jpg" alt="swancpr.jpg">

<p><a href="http://localhost/">Return to Home</a></p>


';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>