<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Friends.. but it\'s all wrong - There I Ruined It';
$meta_description = 'Friends.. but it\'s all wrong';
$meta_keywords = '"#humor", A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Friends.. but it\'s all wrong - There I Ruined It',
  'author' => 'There I Ruined It',
  '"Date' => '": March 31, 2025',
  'tags' => 
  array (
    0 => '"#humor"',
  ),
  'excerpt' => 'Friends.. but it\'s all wrong',
  'categories' => 
  array (
    0 => 'Humor',
  ),
);

// Raw content
$post_content = '<iframe width="560" height="315" src="https://www.youtube.com/embed/CDArF8zUmMY?si=EZ0suXpeTsPJsdr3" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>

<h1>Friends…but it’s all wrong</h1>

<p><a href="https://yt3.ggpht.com/ENAGFylOD6TmwEq2eZRR84JY2FydyEWHybqkZuMeZSjiiSmSo1P-kRBg7puYo1DRnrXZgMo=s88-c-k-c0x00ffffff-no-rj" class="external-link"><img src="https://www.youtube.com/@ThereIRuinedIt" alt="</a>"></p>

<p><a href="https://www.youtube.com/@ThereIRuinedIt" class="external-link">There I Ruined It</a></p>

<p>1.24M subscribers</p>
<p>218,557 views Mar 31, 2025</p>

<p>No description has been added to this video.</p>

<p><a href="https://www.youtube.com/shorts/CDArF8zUmMY" class="external-link">Friends…but it’s all wrong - YouTube</a></p>';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>