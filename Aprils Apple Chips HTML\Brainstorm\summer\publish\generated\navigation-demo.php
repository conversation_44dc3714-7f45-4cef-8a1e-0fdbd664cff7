<?php
// Auto-generated blog post
// Source: unknown

// Load configuration
$config = include '../config.php';

// Page variables
$page_title = 'Navigation Demo - Enhanced Internal Linking';
$meta_description = 'A demonstration of the enhanced navigation features including internal links, table of contents, and improved link styling.';
$meta_keywords = 'demo, navigation, features, A. A. Chips, blog, personal stories, advocacy, homelessness, family alienation, rebuilding life';
$css_path = '../css/';
$js_path = '../js/';
$base_url = '../';
$related_posts = [];

// Post metadata
$post_data = array (
  'title' => 'Navigation Demo - Enhanced Internal Linking',
  'author' => 'A. A. Chips',
  'date' => '2025-01-15',
  'excerpt' => 'A demonstration of the enhanced navigation features including internal links, table of contents, and improved link styling.',
  'tags' => 
  array (
    0 => 'demo',
    1 => 'navigation',
    2 => 'features',
  ),
);

// Raw content
$post_content = '<h1>Navigation Demo - Enhanced Internal Linking</h1>

<p>This post demonstrates the enhanced navigation features that have been implemented to improve the user experience and make internal linking more seamless.</p>

<h2>Overview of Enhancements</h2>

<p>The navigation system now includes several key improvements:</p>

<h3>Internal Link Processing</h3>
<p>Wiki-style links like <a href="contents.php" class="internal-link">contents</a> and <a href="about-me.php" class="internal-link">About Me</a> are automatically processed and styled to blend better with the text content. These links use a subtle highlight background and dotted underline instead of button-style formatting.</p>

<h3>External Link Indicators</h3>
<p>External links like <a href="https://www.ko-fi.com/aachips" class="external-link">Ko-Fi page</a> automatically get an arrow indicator (↗) to show they lead outside the site.</p>

<h3>Table of Contents</h3>
<p>For longer posts with multiple headings, a table of contents is automatically generated and positioned on the right side of the content.</p>

<h2>Navigation Features</h2>

<h3>Breadcrumb Navigation</h3>
<p>The sidebar now includes breadcrumb navigation to help users understand their current location within the site hierarchy.</p>

<h3>Enhanced Related Posts</h3>
<p>Related posts are displayed in an improved grid layout with excerpts and metadata.</p>

<h3>Quick Navigation</h3>
<p>The sidebar includes quick links to major content indices:</p>
<ul><li><a href="contents.php" class="internal-link">Site Contents</a></li>
<p><li><a href="main-index.php" class="internal-link">Main Index</a></li></p>
<p><li><a href="humor-vault.php" class="internal-link">Humor Vault</a></li></p>
<p><li><a href="inspiration-vault.php" class="internal-link">Inspiration Vault</a></li></p>

<h3>Back to Top Button</h3>
<p>A floating back-to-top button appears when scrolling down, providing easy navigation back to the top of long posts.</p>

<h2>Link Styling Examples</h2>

<p>Here are examples of different link types:</p>

<p><li><strong>Internal links</strong>: <a href="adhd-in-women.php" class="internal-link">adhd-in-women</a> and <a href="alternatives-to-iloveyou.php" class="internal-link">Alternatives to &quot;I Love You&quot;</a></li></p>
<p><li><strong>External links</strong>: <a href="http://upworthy.com/rednote-letters-from-li-hua" class="external-link">Upworthy article</a></li></p>
<p><li><strong>Category links</strong>: Links in the sidebar use the same internal styling</li></p>

<h2>Content Organization</h2>

<p>The enhanced navigation makes it easier to explore related content:</p>

<h3>Family & Relationships</h3>
<p><li><a href="200-blunt-words.php" class="internal-link">200 Blunt Words</a></li></p>
<p><li><a href="must-watch-divorcing-parents.php" class="internal-link">Must Watch for Divorcing Parents</a></li></p>
<p><li><a href="lgbt-youth-wellness.php" class="internal-link">LGBT Youth Wellness</a></li></p>

<h3>Advocacy & Social Justice</h3>
<p><li><a href="survivor-manifesto.php" class="internal-link">Survivor Manifesto</a></li></p>
<p><li><a href="not-time-to-acquiesce.php" class="internal-link">Not Time to Acquiesce</a></li></p>
<p><li><a href="wellness-checks.php" class="internal-link">Wellness Checks</a></li></p>

<h3>Personal Stories</h3>
<p><li><a href="hadestown-review.php" class="internal-link">Hadestown Review</a></li></p>
<p><li><a href="finding-way-back.php" class="internal-link">Finding Way Back</a></li></p>
<p><li><a href="i-am-not-smee.php" class="internal-link">I Am Not Smee</a></li></ul></p>

<h2>Technical Implementation</h2>

<p>The enhancements include:</p>

<p>1. <strong>CSS improvements</strong> for better link integration</p>
<p>2. <strong>JavaScript functionality</strong> for dynamic features</p>
<p>3. <strong>Build system updates</strong> to process wiki-style links</p>
<p>4. <strong>Responsive design</strong> for mobile compatibility</p>

<p>This creates a more interconnected and navigable content experience, encouraging exploration while maintaining readability.</p>
';

// Generate dynamic content
ob_start();
?>
<article class="post-header">
    <?php if (isset($post_data['title'])): ?>
        <h1 class="post-title"><?php echo htmlspecialchars($post_data['title']); ?></h1>
    <?php endif; ?>
    
    <?php $metadata = []; ?>
    <?php if (isset($post_data['author'])): ?>
        <?php $metadata[] = '<span class="post-author"><i class="icon-user"></i>By ' . htmlspecialchars($post_data['author']) . '</span>'; ?>
    <?php endif; ?>
    <?php if (isset($post_data['date'])): ?>
        <?php $formatted_date = (strtotime($post_data['date']) !== false) ? date('F j, Y', strtotime($post_data['date'])) : htmlspecialchars($post_data['date']); ?>
        <?php $metadata[] = '<span class="post-date"><i class="icon-calendar"></i>' . $formatted_date . '</span>'; ?>
    <?php endif; ?>
    
    <?php if (!empty($metadata)): ?>
        <div class="post-meta"><?php echo implode('<span class="meta-separator"> • </span>', $metadata); ?></div>
    <?php endif; ?>
    
    <?php if (isset($post_data['excerpt'])): ?>
        <div class="post-excerpt">
            <p><em><?php echo htmlspecialchars($post_data['excerpt']); ?></em></p>
        </div>
    <?php endif; ?>
    
    <?php if (isset($post_data['tags'])): ?>
        <?php $tags = is_array($post_data['tags']) ? $post_data['tags'] : [$post_data['tags']]; ?>
        <?php if (!empty($tags)): ?>
            <div class="post-tags">
                <span class="tags-label">Tags:</span>
                <?php foreach ($tags as $tag): ?>
                    <?php $tag_slug = strtolower(preg_replace('/[^a-z0-9-]/', '-', preg_replace('/-+/', '-', trim($tag)))); ?>
                    <a href="tag-<?php echo $tag_slug; ?>.html" class="tag"><?php echo htmlspecialchars($tag); ?></a>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</article>

<div class="post-content">
    <?php echo $post_content; ?>
</div><!-- .post-content -->
<?php
$content = ob_get_clean();

// Include template
include '../page template.htm';
?>